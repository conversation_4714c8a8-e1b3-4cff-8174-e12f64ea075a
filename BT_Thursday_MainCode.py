import os
#import re
import time
from datetime import datetime, timedelta
from BT_Thursday_New_Recreate_Strategy import thursday_execute_BCS
from Monitor_Prices import monitor_prices
#import logger_config
#import random
#from datetime import date
#from Generate_Equity_Curve import generate_equity_curve
#import csv
#from ratio_hit_analysis import export_ratio_hit_analysis

#logger = logger_config.setup_logger('main_logger', 'main.log')


# ------------- 1. isolate the *original* single‑date routine ---------------- #
def run_for_one_day(main_folder: str, start_time, exit_time,stop_loss, target_profit, ratio_check, resize_factor, data) -> None:
    """
    Run the existing back‑test pipeline for a single YYYYMMDD folder.
    This is your original code, moved unchanged into its own function.
    """
    start_time_process = time.time()

    # --- Identify the date from the folder name --- #
    folder_date = os.path.basename(main_folder)
    folder_date_dt = datetime.strptime(folder_date, "%Y%m%d")
    #logger.info(f"Folder Date: {folder_date_dt.strftime('%Y-%m-%d')}")

    # --- Extract folder-specific data from the data dictionary --- #
    folder_data = data[main_folder]

    # --- Subfolders and their sides (unchanged) --- #
    sides = {
        "CE_SELL": "SELL",
        "PE_SELL": "SELL",
        "CE_BUY":  "BUY",
        "PE_BUY":  "BUY"
    }


    # --- Strategy + monitoring (unchanged) --- #
    start_execution_time = time.time()
    BCS_positions, vix_close_value = thursday_execute_BCS(main_folder, start_time, exit_time, folder_date_dt,stop_loss, target_profit, ratio_check, resize_factor, folder_data)
    
    if BCS_positions is None:
        #logger.info(f"Skipping folder {main_folder} due to missing VIX data or other issues.")
        return None, None
    #logger.info(f"BCS_positions: {BCS_positions}")
    #logger.info(f"thursday_execute_BCS() execution time: {time.time()-start_execution_time:.6f}s")

    start_execution_time = time.time()
    total_pnl, exit_reason  =monitor_prices(BCS_positions, main_folder, start_time, exit_time, folder_date, target_profit, stop_loss,ratio_check, folder_data)
    #logger.info(f"monitor_prices() execution time: {time.time()-start_execution_time:.6f}s")

    # --- Finish up --- #
    processing_time = time.time() - start_time_process
    #logger.info(f"Processing time: {str(timedelta(seconds=int(processing_time)))}")
    #logger.info("End of day ----------------------------------------------\n")
    
    return folder_date,total_pnl, exit_reason, vix_close_value

