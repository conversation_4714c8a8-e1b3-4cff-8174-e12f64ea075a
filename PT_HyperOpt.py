import os
import random
import csv
import re
import numpy as np
import pandas as pd
from datetime import datetime
from hyperopt import fmin, tpe, hp, Trials, STATUS_OK
from BT_Thursday_MainCode import run_for_one_day
from Matrix import *
from dotenv import load_dotenv
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import multiprocessing

load_dotenv()

# Function to load Parquet files into memory
def load_parquet_data(folders):
    data = {}
    for folder in folders:
        data[folder] = {}
        for subfolder in ['CE_BUY', 'CE_SELL', 'PE_BUY', 'PE_SELL']:
            subfolder_path = os.path.join(folder, subfolder)
            parquet_files = [f for f in os.listdir(subfolder_path) if f.endswith('.parquet')]
            data[folder][subfolder] = [pd.read_parquet(os.path.join(subfolder_path, f)) for f in parquet_files]
    return data

# ------------------ Helper to Remove Duplicate Trials ------------------ #
def get_unique_sorted_trials(trials):
    seen = set()
    unique_trials = []
    for trial in sorted(trials.results, key=lambda x: x['loss']):
        param_tuple = tuple(sorted(trial['params'].items()))
        if param_tuple not in seen:
            seen.add(param_tuple)
            unique_trials.append(trial)
    return unique_trials


# ------------------ Save ALL Unique Trials to CSV ------------------ #
def save_results_to_csv(trials, file_path):
    seen = set()
    unique_rows = []

    for trial in trials.results:
        params = trial['params']
        param_tuple = tuple(sorted(params.items()))
        if param_tuple not in seen:
            seen.add(param_tuple)
            unique_rows.append({
                'start_time': params['start_time'],
                'exit_time': params['exit_time'],
                'stop_loss': params['stop_loss'],
                'target_profit': params['target_profit'],
                'ratio_check': params['ratio_check'],
                'resize_factor': params['resize_factor'],
                'total_pnl': trial.get('total_pnl', 0),
                'max_drawdown': trial.get('max_drawdown', 0),
                'final_score': -trial.get('loss', 0)
            })

    # ✅ Sort by final_score descending
    unique_rows.sort(key=lambda x: x['final_score'], reverse=True)

    with open(file_path, mode='w', newline='') as file:
        writer = csv.DictWriter(file, fieldnames=[
            'start_time', 'exit_time', 'stop_loss', 'target_profit',
            'ratio_check', 'resize_factor', 'total_pnl',
            'max_drawdown', 'final_score'
        ])
        writer.writeheader()
        writer.writerows(unique_rows)


# ------------------ Objective Function ------------------ #
def objective(params, data):
    start_time = datetime.strptime(params['start_time'], "%H:%M").time()
    exit_time = datetime.strptime(params['exit_time'], "%H:%M").time()
    stop_loss = params['stop_loss']
    target_profit = params['target_profit']
    ratio_check = params['ratio_check']
    resize_factor = params['resize_factor']

    def run_folder(folder, start_time, exit_time, stop_loss, target_profit, ratio_check, resize_factor):
        try:
            # Get folder-specific data from the data dictionary
            folder_data = data[folder]
            return run_for_one_day(folder, start_time, exit_time, stop_loss, target_profit, ratio_check, resize_factor, folder_data)
        except Exception as e:
            print(f"Error in folder {folder}: {e}")
            return None, None, None, None

    max_workers = min(8, multiprocessing.cpu_count())

    # Get folder names from data keys
    all_folders = list(data.keys())

    # Run in parallel
    with ThreadPoolExecutor(max_workers) as executor:  # You can adjust the number of threads
        futures = {
            executor.submit(run_folder, folder, start_time, exit_time, stop_loss, target_profit, ratio_check, resize_factor): folder
            for folder in all_folders
        }

    train_rows = []
    total_pnl = 0

    for future in as_completed(futures):
        date_str, pnl, exit_reason, vix_close_value = future.result()
        if pnl is not None:
            train_rows.append({'date': date_str, 'pnl': pnl})
            total_pnl += pnl

    df_train = pd.DataFrame(train_rows)
    print(df_train)
    df_train = df_train.sort_values('date')
    df_train['pnl'] = pd.to_numeric(df_train['pnl'], errors='coerce').fillna(0)
    df_train['equity'] = df_train['pnl'].cumsum()

    metrics = process_performance_metrics(df_train)
    max_drawdown = metrics.get('max_drawdown', 0)

    Capital = int(os.getenv('Capital'))
    normalized_pnl = total_pnl / Capital

    #final_score = (normalized_pnl + (1- max_drawdown))
    
    #risk_penalty = np.exp(5 * max_drawdown)  # Penalty for high DD
    #final_score = normalized_pnl / risk_penalty

    if max_drawdown > 0.09:
        risk_penalty = np.exp(15 * (max_drawdown - 0.09)) + 1
    else:
        risk_penalty = 1  # No penalty if within your target
    
    final_score = normalized_pnl/risk_penalty
    
    
    return {
        'loss': -final_score,
        'status': STATUS_OK,
        'params': params,
        'total_pnl': total_pnl,
        'max_drawdown': max_drawdown
    }


# ------------------ Folder Filtering ------------------ #
def list_date_folders(root_path, from_date, to_date):
    date_folder_pattern = re.compile(r"^\d{8}$")
    all_entries = [
        os.path.join(root_path, name)
        for name in os.listdir(root_path)
        if date_folder_pattern.match(name) and os.path.isdir(os.path.join(root_path, name))
    ]
    all_entries.sort()
    filtered_entries = [
        f for f in all_entries
        if from_date <= datetime.strptime(os.path.basename(f), "%Y%m%d") <= to_date
    ]

    valid_folders = []
    skipped_dates = []

    for folder in filtered_entries:
        date_str = os.path.basename(folder)
        subfolders = ['CE_BUY', 'CE_SELL', 'PE_BUY', 'PE_SELL']
        valid = all(
            os.path.exists(os.path.join(folder, sub)) and
            any(f.endswith('.parquet') for f in os.listdir(os.path.join(folder, sub)))
            for sub in subfolders
        )
        if valid:
            valid_folders.append(folder)
        else:
            skipped_dates.append(date_str)

    with open('skipped_folders.csv', mode='w', newline='') as file:
        writer = csv.writer(file)
        writer.writerow(['Skipped_Folder_Date'])
        for date in skipped_dates:
            writer.writerow([date])

    print(f"Found {len(valid_folders)} valid folders between {from_date.date()} and {to_date.date()}")
    print(f"Skipped {len(skipped_dates)} folders due to missing or empty subfolders.")
    
    return valid_folders


# ------------------ Main Optimization Routine ------------------ #
def run_hyperopt():

    root_path = r"C:\Users\<USER>\YatinBhatia\Thursday_BT_Performance_Tuning\Parquet_Files\Thursday_output_folder"

    all_folders = list_date_folders(root_path, datetime(2021, 1, 1), datetime(2021, 1, 15))

    # Load all Parquet data into memory
    data = load_parquet_data(all_folders)
    print(type(data))

    # Assuming 'data' is a dictionary of DataFrames
    for folder, subfolders in data.items():
        print(f"Folder: {folder}")
        for subfolder, df_list in subfolders.items():
            print(f"  Subfolder: {subfolder}")
            for i, df in enumerate(df_list):
                print("      Columns:", df.columns)
                print("      Head:\n", df.head())
              

    space = {
        'start_time': hp.choice('start_time', [f"09:{m:02d}" for m in range(30, 60, 5)]),
        'exit_time': hp.choice('exit_time', [f"14:{m:02d}" for m in range(45, 60, 5)] + [f"15:{m:02d}" for m in range(0, 20, 5)]),
        'stop_loss': hp.choice('stop_loss', [-0.5 - 0.1 * i for i in range(11)]),
        #'stop_loss': hp.choice('stop_loss', [-0.3 - 0.05 * i for i in range(8)]),  # Tighter stop losses
        'target_profit': hp.choice('target_profit', [1.0 + 0.1 * i for i in range(11)]),
        'ratio_check': hp.choice('ratio_check', [0.10 + 0.01 * i for i in range(11)]),
        'resize_factor': hp.choice('resize_factor', [0.3 + 0.1 * i for i in range(5)])
    }

    trials = Trials()
    best = fmin(
        fn=lambda params: objective(params, data),
        space=space,
        algo=tpe.suggest,
        max_evals=20,
        trials=trials,
    )

    print(f"Best Parameters Index: {best}")

    # Save all unique results
    save_results_to_csv(trials, 'hyperopt_results.csv')

    # Top 3 unique trials
    top_trials = get_unique_sorted_trials(trials)[:3]

    print("\nTop 5 Best Parameters:")
    for i, trial in enumerate(top_trials):
        params = trial['params']
        print(f"\nRank {i+1}:")
        for param, value in params.items():
            print(f"{param}: {value}")
        print(f"Total PnL: {trial.get('total_pnl', 0)}")
        print(f"Max Drawdown: {trial.get('max_drawdown', 0)}")
        print(f"Final Score: {-trial.get('loss', 0)}")

 
# ------------------ Entry Point ------------------ #
if __name__ == "__main__":
    import time
    start = time.time()
    print("Starting hyperparameter optimization...")
    run_hyperopt()
    print(f"Total execution time: {time.time() - start:.2f}s")
